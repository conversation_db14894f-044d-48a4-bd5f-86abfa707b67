<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgroTech Dashboard - Commodities Agrícolas</title>
    <meta name="description" content="Dashboard para análise de preços de commodities agrícolas: boi, soja e milho">

    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- Fallback para Chart.js -->
    <script src="chart-fallback.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌾</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1 class="logo">
                <span class="logo-icon">🌾</span>
                AgroTech Dashboard
            </h1>
            <div class="header-info">
                <span id="last-update" class="last-update">Carregando...</span>
                <button id="refresh-btn" class="refresh-btn" title="Atualizar dados">
                    <span class="refresh-icon">🔄</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">

            <!-- Loading State -->
            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <p>Carregando dados das commodities...</p>
            </div>

            <!-- Error State -->
            <div id="error" class="error-message" style="display: none;">
                <h3>❌ Erro ao carregar dados</h3>
                <p id="error-text">Não foi possível conectar com o servidor.</p>
                <button onclick="loadDashboard()" class="retry-btn">Tentar novamente</button>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboard" class="dashboard" style="display: none;">

                <!-- Current Values Summary -->
                <section class="summary-section">
                    <h2 class="section-title">Valores Atuais</h2>
                    <div class="summary-cards">
                        <div class="summary-card boi-card">
                            <div class="card-header">
                                <span class="card-icon">🐄</span>
                                <h3>Arroba do Boi</h3>
                            </div>
                            <div class="card-content">
                                <div class="price" id="boi-price">R$ --</div>
                                <div class="date" id="boi-date">--</div>
                                <div class="source" id="boi-source">--</div>
                            </div>
                        </div>

                        <div class="summary-card soja-card">
                            <div class="card-header">
                                <span class="card-icon">🌱</span>
                                <h3>Saca de Soja</h3>
                            </div>
                            <div class="card-content">
                                <div class="price" id="soja-price">R$ --</div>
                                <div class="date" id="soja-date">--</div>
                                <div class="source" id="soja-source">--</div>
                            </div>
                        </div>

                        <div class="summary-card milho-card">
                            <div class="card-header">
                                <span class="card-icon">🌽</span>
                                <h3>Saca de Milho</h3>
                            </div>
                            <div class="card-content">
                                <div class="price" id="milho-price">R$ --</div>
                                <div class="date" id="milho-date">--</div>
                                <div class="source" id="milho-source">--</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Charts Section -->
                <section class="charts-section">
                    <h2 class="section-title">Histórico de Preços (Últimos 30 dias)</h2>

                    <div class="charts-grid">
                        <!-- Boi Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>🐄 Arroba do Boi Gordo</h3>
                                <span class="chart-subtitle">Preço em R$ por arroba</span>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="boi-chart"></canvas>
                            </div>
                        </div>

                        <!-- Soja Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>🌱 Saca de Soja</h3>
                                <span class="chart-subtitle">Preço em R$ por saca de 60kg</span>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="soja-chart"></canvas>
                            </div>
                        </div>

                        <!-- Milho Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>🌽 Saca de Milho</h3>
                                <span class="chart-subtitle">Preço em R$ por saca de 60kg</span>
                            </div>
                            <div class="chart-wrapper">
                                <canvas id="milho-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>

            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 AgroTech Dashboard. Dados atualizados diariamente às 07:00.</p>
            <p class="disclaimer">
                <small>
                    Fontes: CEPEA/ESALQ-USP (Boi), Notícias Agrícolas (Grãos).
                    Os preços são indicativos e podem variar conforme região e qualidade.
                </small>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="app.js"></script>
</body>
</html>
